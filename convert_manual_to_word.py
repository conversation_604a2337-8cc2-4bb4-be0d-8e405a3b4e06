#!/usr/bin/env python3
"""
Script to convert SPRODETA M&E User Manual from Markdown to Word document
"""

import re
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

def create_styled_document():
    """Create a new Word document with custom styles"""
    doc = Document()
    
    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)
    
    # Create custom styles
    styles = doc.styles
    
    # Title style
    title_style = styles.add_style('CustomTitle', WD_STYLE_TYPE.PARAGRAPH)
    title_font = title_style.font
    title_font.name = 'Arial'
    title_font.size = Pt(24)
    title_font.bold = True
    title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_style.paragraph_format.space_after = Pt(12)
    
    # Subtitle style
    subtitle_style = styles.add_style('CustomSubtitle', WD_STYLE_TYPE.PARAGRAPH)
    subtitle_font = subtitle_style.font
    subtitle_font.name = 'Arial'
    subtitle_font.size = Pt(14)
    subtitle_font.bold = True
    subtitle_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
    subtitle_style.paragraph_format.space_after = Pt(18)
    
    # Heading 1 style
    heading1_style = styles.add_style('CustomHeading1', WD_STYLE_TYPE.PARAGRAPH)
    heading1_font = heading1_style.font
    heading1_font.name = 'Arial'
    heading1_font.size = Pt(18)
    heading1_font.bold = True
    heading1_font.color.rgb = None  # Default color
    heading1_style.paragraph_format.space_before = Pt(18)
    heading1_style.paragraph_format.space_after = Pt(12)
    
    # Heading 2 style
    heading2_style = styles.add_style('CustomHeading2', WD_STYLE_TYPE.PARAGRAPH)
    heading2_font = heading2_style.font
    heading2_font.name = 'Arial'
    heading2_font.size = Pt(16)
    heading2_font.bold = True
    heading2_style.paragraph_format.space_before = Pt(12)
    heading2_style.paragraph_format.space_after = Pt(6)
    
    # Heading 3 style
    heading3_style = styles.add_style('CustomHeading3', WD_STYLE_TYPE.PARAGRAPH)
    heading3_font = heading3_style.font
    heading3_font.name = 'Arial'
    heading3_font.size = Pt(14)
    heading3_font.bold = True
    heading3_style.paragraph_format.space_before = Pt(6)
    heading3_style.paragraph_format.space_after = Pt(6)
    
    # Body text style
    body_style = styles.add_style('CustomBody', WD_STYLE_TYPE.PARAGRAPH)
    body_font = body_style.font
    body_font.name = 'Arial'
    body_font.size = Pt(11)
    body_style.paragraph_format.space_after = Pt(6)
    body_style.paragraph_format.line_spacing = 1.15
    
    # List style
    list_style = styles.add_style('CustomList', WD_STYLE_TYPE.PARAGRAPH)
    list_font = list_style.font
    list_font.name = 'Arial'
    list_font.size = Pt(11)
    list_style.paragraph_format.left_indent = Inches(0.25)
    list_style.paragraph_format.space_after = Pt(3)
    
    return doc

def parse_markdown_content(content):
    """Parse markdown content and return structured data"""
    lines = content.split('\n')
    parsed_content = []
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            i += 1
            continue
            
        # Main title (# SPRODETA...)
        if line.startswith('# ') and 'SPRODETA' in line:
            parsed_content.append({
                'type': 'title',
                'content': line[2:].strip()
            })
        # Version info and metadata
        elif line.startswith('**Version:**') or line.startswith('**Last Updated:**') or line.startswith('**System Version:**'):
            parsed_content.append({
                'type': 'subtitle',
                'content': line.replace('**', '').strip()
            })
        # Horizontal rules
        elif line.startswith('---'):
            parsed_content.append({
                'type': 'separator',
                'content': ''
            })
        # Headings
        elif line.startswith('## '):
            parsed_content.append({
                'type': 'heading1',
                'content': line[3:].strip()
            })
        elif line.startswith('### '):
            parsed_content.append({
                'type': 'heading2',
                'content': line[4:].strip()
            })
        elif line.startswith('#### '):
            parsed_content.append({
                'type': 'heading3',
                'content': line[5:].strip()
            })
        # Lists
        elif line.startswith('- ') or line.startswith('* '):
            parsed_content.append({
                'type': 'list_item',
                'content': line[2:].strip()
            })
        elif re.match(r'^\d+\. ', line):
            parsed_content.append({
                'type': 'numbered_list',
                'content': re.sub(r'^\d+\. ', '', line).strip()
            })
        # Bold text (standalone)
        elif line.startswith('**') and line.endswith('**') and len(line) > 4:
            parsed_content.append({
                'type': 'bold_text',
                'content': line[2:-2].strip()
            })
        # Regular paragraph
        elif line:
            parsed_content.append({
                'type': 'paragraph',
                'content': line
            })
        
        i += 1
    
    return parsed_content

def add_content_to_document(doc, parsed_content):
    """Add parsed content to the Word document"""
    
    for item in parsed_content:
        content_type = item['type']
        content = item['content']
        
        if content_type == 'title':
            p = doc.add_paragraph(content, style='CustomTitle')
            
        elif content_type == 'subtitle':
            p = doc.add_paragraph(content, style='CustomSubtitle')
            
        elif content_type == 'separator':
            # Add some space instead of a line
            doc.add_paragraph()
            
        elif content_type == 'heading1':
            p = doc.add_paragraph(content, style='CustomHeading1')
            
        elif content_type == 'heading2':
            p = doc.add_paragraph(content, style='CustomHeading2')
            
        elif content_type == 'heading3':
            p = doc.add_paragraph(content, style='CustomHeading3')
            
        elif content_type == 'list_item':
            p = doc.add_paragraph(style='CustomList')
            p.add_run('• ' + content)
            
        elif content_type == 'numbered_list':
            p = doc.add_paragraph(style='CustomList')
            p.add_run('• ' + content)  # Using bullet for simplicity
            
        elif content_type == 'bold_text':
            p = doc.add_paragraph(style='CustomBody')
            run = p.add_run(content)
            run.bold = True
            
        elif content_type == 'paragraph':
            # Handle inline formatting
            p = doc.add_paragraph(style='CustomBody')
            
            # Simple bold text handling
            parts = re.split(r'(\*\*.*?\*\*)', content)
            for part in parts:
                if part.startswith('**') and part.endswith('**'):
                    run = p.add_run(part[2:-2])
                    run.bold = True
                else:
                    p.add_run(part)

def main():
    """Main function to convert markdown to Word document"""
    
    # Read the markdown file
    try:
        with open('SPRODETA_ME_User_Manual.md', 'r', encoding='utf-8') as f:
            markdown_content = f.read()
    except FileNotFoundError:
        print("Error: SPRODETA_ME_User_Manual.md file not found!")
        return
    
    # Create styled document
    doc = create_styled_document()
    
    # Parse markdown content
    parsed_content = parse_markdown_content(markdown_content)
    
    # Add content to document
    add_content_to_document(doc, parsed_content)
    
    # Save the document
    output_filename = 'SPRODETA_ME_User_Manual.docx'
    doc.save(output_filename)
    
    print(f"Successfully converted user manual to Word document: {output_filename}")
    print(f"Document contains {len(parsed_content)} content elements")

if __name__ == "__main__":
    main()
